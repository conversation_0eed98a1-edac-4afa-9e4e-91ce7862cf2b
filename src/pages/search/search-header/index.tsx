import { useEffect, useMemo, useState } from "react";
import { ChevronDown, Clock, Plane } from "lucide-react";
import type { SearchData, SelectOption } from "./types";
import Select from "react-select";
import { selectStyles } from "@/components/search/style.ts";
import DatePicker from "react-datepicker";
import { useNavigate, useSearchParams } from "react-router";
import { getIntervalTime, utcInIso } from "@/utils";
import { useToast } from "@/components/toast/ToasterProvider";
import useAxios from "@/hooks/useAxios.tsx";
import type { ILocation } from "@/types";

const SearchHeader = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [searchParams] = useSearchParams();
  const airportLocation = searchParams.get("location") ?? "";
  const checkInDate = searchParams.get("checkIn") ?? new Date();
  const checkOutDate =
    searchParams.get("checkOut") ?? getIntervalTime(new Date());

  const [searchData, setSearchData] = useState<SearchData>({
    location: null,
    checkIn: checkInDate,
    checkOut: checkOutDate,
  });

  const {
    data: locations,
    loading: isLocationLoading,
    request: getLocations,
    error: locationError,
  } = useAxios<ILocation[]>();

  const handleSearch = () => {
    if (!searchData.location) {
      toast.show("Please select a location", { type: "error" });
      return;
    }
    if (!searchData.checkIn) {
      toast.show("Please select a check-in date", { type: "error" });
      return;
    }
    if (!searchData.checkOut) {
      toast.show("Please select a check-out date", { type: "error" });
      return;
    }

    const queryParams = new URLSearchParams();
    queryParams.set("location", searchData.location?.value ?? "");
    queryParams.set("checkIn", utcInIso(searchData.checkIn));
    queryParams.set("checkOut", utcInIso(searchData.checkOut));
    navigate(
      {
        pathname: "/search",
        search: queryParams.toString(),
      },
      { replace: true },
    );
  };

  useEffect(() => {
    const getLocationsData = async () => {
      await getLocations({
        url: "/locations",
        method: "GET",
      });
    };
    getLocationsData();
  }, []);

  useEffect(() => {
    if (locationError) {
      toast.show(locationError, { type: "error" });
    }
  }, [locationError]);

  const locationOptions: SelectOption[] = useMemo(() => {
    return (
      locations?.map((loc) => ({
        label: `${loc.city}, ${loc.country}`,
        value: loc._id,
      })) || []
    );
  }, [locations]);

  useEffect(() => {
    if (locationOptions && locationOptions.length > 0) {
      setSearchData((prev: SearchData) => ({
        ...prev,
        location:
          locationOptions.find((loc) => loc.value === airportLocation) ?? null,
      }));
    }
  }, [locationOptions]);

  if (isLocationLoading) return <div>Loading...</div>;

  return (
    <div className="flex flex-col lg:flex-row w-full rounded-2xl mb-6 gap-4">
      <div className="bg-white w-full rounded-2xl shadow-md  flex flex-col md:flex-row md:items-center md:justify-between border border-gray-100 hover:shadow-lg transition-shadow duration-300">
        {/* Location - Full width on mobile */}
        <div className="flex items-center gap-3 flex-1 px-4 border-b md:border-b-0 md:border-r border-gray-200 group hover:bg-gray-50 rounded-xl md:rounded-none md:rounded-l-xl transition-colors duration-200">
          <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-white transition-colors duration-200">
            <Plane className="w-5 h-5 text-gray-600" />
          </div>
          <div className="flex-1 relative">
            <label className="block text-xs font-medium text-gray-500 mb-1">
              Location
            </label>
            <div className="relative mb-1">
              <Select
                options={locationOptions}
                value={searchData.location}
                className="w-full font-medium"
                onChange={(option) =>
                  setSearchData((prev) => ({
                    ...prev,
                    location: option,
                  }))
                }
                styles={selectStyles}
                placeholder="Select a location"
              />
            </div>
            {/*<p className="text-xs text-gray-500 mt-1">Bengaluru, India</p>*/}
          </div>
        </div>

        {/* Time inputs - Side by side on mobile */}
        <div className="flex flex-col sm:flex-row">
          <div className="flex items-center gap-3 flex-1 px-4 border-b sm:border-b-0 sm:border-r border-gray-200 group hover:bg-gray-50 rounded-xl sm:rounded-none transition-colors duration-200">
            <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-white transition-colors duration-200">
              <Clock className="w-5 h-5 text-gray-600" />
            </div>
            <div className="flex-1 relative">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Check-in
              </label>
              <div className="relative">
                <DatePicker
                  selected={new Date(searchData.checkIn || new Date())}
                  onChange={(date) => {
                    if (!date) return;
                    setSearchData((prev) => ({
                      ...prev,
                      checkIn: date,
                      checkOut: getIntervalTime(date),
                    }));
                  }}
                  minDate={new Date()}
                  showTimeSelect
                  timeIntervals={15}
                  timeFormat="HH:mm"
                  dateFormat="dd-MM-yyyy HH:mm"
                  className="w-full text-md bg-transparent font-medium !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                  placeholderText="Select check-in date and time"
                />
                <ChevronDown className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 flex-1 px-4 group hover:bg-gray-50 rounded-xl md:rounded-none md:rounded-r-xl transition-colors duration-200">
            <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-white transition-colors duration-200">
              <Clock className="w-5 h-5 text-gray-600" />
            </div>
            <div className="flex-1 relative">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Check-out
              </label>
              <div className="relative">
                <DatePicker
                  selected={new Date(searchData.checkOut || new Date())}
                  onChange={(date) =>
                    setSearchData((prev) => ({
                      ...prev,
                      checkOut: date,
                    }))
                  }
                  minDate={new Date()}
                  showTimeSelect
                  timeIntervals={15}
                  timeFormat="HH:mm"
                  dateFormat="dd-MM-yyyy HH:mm"
                  className="w-full text-md bg-transparent font-medium !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                  placeholderText="Select check-in date and time"
                />
                <ChevronDown className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={handleSearch}
        className="btn-primary text-white font-medium  px-2 rounded-xl text-lg transition-all duration-200 hover:scale-105 shadow-xl"
      >
        Search
      </button>
    </div>
  );
};

export default SearchHeader;
