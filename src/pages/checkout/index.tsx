import GuestForm from "@/pages/checkout/guest-form";
import Header from "@/components/header";
import CheckoutCard from "@/components/checkout-card";
import type { GuestFormValues } from "@/pages/checkout/guest-form/type";
import { FormProvider, useForm } from "react-hook-form";

const CheckoutPage = () => {
  const methods = useForm<GuestFormValues>({
    defaultValues: {
      guests: [
        {
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        },
      ],
      flightNumber: "",
      fromLocation: "",
      toLocation: "",
      departureDateTime: "",
      arrivalDateTime: "",
    },
  });

  const onSubmit = (_data: GuestFormValues) => {};

  return (
    <div>
      <Header />
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmit)}
          className="w-full bg-white"
        >
          <div className="px-6 py-4 flex flex-col md:flex-row gap-4 justify-between">
            <div className="w-full flex-3">
              <GuestForm />
            </div>
            <div className="w-full flex-1">
              <h1 className={"text-lg mb-2 font-medium"}>Your Stay Summary</h1>
              <CheckoutCard
                imageUrl="https://picsum.photos/200/300?random=13"
                hotelName="Happy Stays"
                roomType="Single deluxe"
                capacity={2}
                bedType="Double Bed"
                rate={49}
                taxesFees={2}
                checkIn="12:00 PM"
                checkOut="04:00 PM"
              />
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default CheckoutPage;
