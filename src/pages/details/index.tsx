import Header from "@/components/header";
import SearchHeader from "@/pages/search/search-header";
import DetailsBanner from "@/pages/details/banner";
import PackageCard from "@/components/package-card";
import AirportLoader from "@/components/loader";
import SelectedPackagesSummary from "@/components/selected-packages-summary";
import { useEffect, useState } from "react";
import { useParams, useSearchParams, useNavigate } from "react-router";
import { getIntervalTime } from "@/utils";
import useAxios from "@/hooks/useAxios.tsx";
import type { IPropertyDetails } from "@/types";
import type { IPackage } from "@/types/Package.ts";
import { useToast } from "@/components/toast/ToasterProvider";

const DetailsPage = () => {
  const toast = useToast();
  const navigate = useNavigate();
  const {
    data: propertyDetails,
    loading: isPropertyDetailsLoading,
    request: getPropertyDetails,
    error: propertyDetailsError,
  } = useAxios<IPropertyDetails>();

  const {
    data: packages,
    loading: isPackagesLoading,
    request: getPackages,
    error: packagesError,
  } = useAxios<IPackage[]>();
  const [searchParams] = useSearchParams();
  const { propertyId } = useParams();

  const checkInDate = searchParams.get("checkIn") ?? new Date();
  const checkOutDate =
    searchParams.get("checkOut") ?? getIntervalTime(new Date());

  const [selectedPackages, setSelectedPackages] = useState<IPackage[] | null>(
    null,
  );

  const handlePackageChange = (pkg: IPackage, type: "add" | "remove") => {
    setSelectedPackages((prev) => {
      const packages = prev ?? [];

      if (type === "add") {
        return [...packages, pkg];
      }
      const index = packages.findIndex((p) => p._id === pkg._id);
      if (index !== -1) {
        const newPackages = [...packages];
        newPackages.splice(index, 1);
        return newPackages;
      }
      return packages;
    });
  };



  const handleBookNow = () => {
    // Navigate to checkout with selected packages
    navigate("/checkout", {
      state: {
        selectedPackages,
        propertyDetails,
        checkInDate,
        checkOutDate
      }
    });
  };

  useEffect(() => {
    const getPropertyDetailsData = async () => {
      await getPropertyDetails({
        url: `/properties/${propertyId}/property-details`,
        method: "GET",
      });
    };
    getPropertyDetailsData();
  }, [propertyId]);

  useEffect(() => {
    const getPackagesData = async () => {
      await getPackages({
        url: `/reservations/${propertyId}/available`,
        method: "GET",
        params: {
          checkIn: checkInDate,
          checkOut: checkOutDate,
        },
      });
    };
    getPackagesData();
  }, [propertyId, checkInDate, checkOutDate]);

  useEffect(() => {
    if (propertyDetailsError) {
      toast.show(propertyDetailsError, { type: "error" });
    }
    if (packagesError) {
      toast.show(packagesError, { type: "error" });
    }
  }, [propertyDetailsError, packagesError]);

  if (isPropertyDetailsLoading || isPackagesLoading)
    return (
      <AirportLoader message="Loading property details and available packages..." />
    );

  if (!propertyDetails) return <div>Property not found</div>;

  return (
    <>
      <Header />
      <div className={"p-6 pb-32"}>
        <SearchHeader />
        <DetailsBanner propertyDetails={propertyDetails} />
        {packages && (
          <div className={"py-4"}>
            <h1 className={"mb-5 text-xl font-medium"}>Browse Packages</h1>
            <div
              className={"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"}
            >
              {packages.map((pkg) => (
                <PackageCard
                  packageData={pkg}
                  key={pkg._id}
                  onPackageChange={handlePackageChange}
                  selectedPackages={selectedPackages}
                />
              ))}
            </div>
          </div>
        )}
        {/*<div className={"py-4"}>*/}
        {/*    <h1 className={"mb-5 text-xl font-medium"}>Ratings & Reviews</h1>*/}
        {/*    <div className={"grid grid-cols-1 md:grid-cols-2 gap-4"}>*/}
        {/*        {sampleReviews.map((review) => (*/}
        {/*            <ReviewCard review={review}/>*/}
        {/*        ))}*/}
        {/*    </div>*/}
        {/*</div>*/}
      </div>

      {/* Animated Selected Packages Summary */}
      {selectedPackages && selectedPackages.length > 0 && (
        <SelectedPackagesSummary
          selectedPackages={selectedPackages}
          onBookNow={handleBookNow}
        />
      )}
    </>
  );
};

export default DetailsPage;
