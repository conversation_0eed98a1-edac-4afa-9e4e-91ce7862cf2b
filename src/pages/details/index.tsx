import Header from "@/components/header";
import SearchHeader from "@/pages/search/search-header";
import DetailsBanner from "@/pages/details/banner";
import PackageCard from "@/components/package-card";
import { useEffect, useState } from "react";
import { useParams, useSearchParams } from "react-router";
import { getIntervalTime } from "@/utils";
import useAxios from "@/hooks/useAxios.tsx";
import type { IPropertyDetails } from "@/types";
import type { IPackage } from "@/types/Package.ts";
import { useToast } from "@/components/toast/ToasterProvider";

const DetailsPage = () => {
  const toast = useToast();
  const {
    data: propertyDetails,
    loading: isPropertyDetailsLoading,
    request: getPropertyDetails,
    error: propertyDetailsError,
  } = useAxios<IPropertyDetails>();

  const {
    data: packages,
    loading: isPackagesLoading,
    request: getPackages,
    error: packagesError,
  } = useAxios<IPackage[]>();
  const [searchParams] = useSearchParams();
  const { propertyId } = useParams();

  const checkInDate = searchParams.get("checkIn") ?? new Date();
  const checkOutDate =
    searchParams.get("checkOut") ?? getIntervalTime(new Date());

  const [selectedPackages, setSelectedPackages] = useState<IPackage[] | null>(
    null,
  );

  const handlePackageChange = (pkg: IPackage, type: "add" | "remove") => {
    setSelectedPackages((prev) => {
      const packages = prev ?? [];

      if (type === "add") {
        return [...packages, pkg];
      }
      const index = packages.findIndex((p) => p._id === pkg._id);
      if (index !== -1) {
        const newPackages = [...packages];
        newPackages.splice(index, 1);
        return newPackages;
      }
      return packages;
    });
  };

  useEffect(() => {
    const getPropertyDetailsData = async () => {
      await getPropertyDetails({
        url: `/properties/${propertyId}/property-details`,
        method: "GET",
      });
    };
    getPropertyDetailsData();
  }, [propertyId]);

  useEffect(() => {
    const getPackagesData = async () => {
      await getPackages({
        url: `/reservations/${propertyId}/available`,
        method: "GET",
        params: {
          checkIn: checkInDate,
          checkOut: checkOutDate,
        },
      });
    };
    getPackagesData();
  }, [propertyId, checkInDate, checkOutDate]);

  useEffect(() => {
    if (propertyDetailsError) {
      toast.show(propertyDetailsError, { type: "error" });
    }
    if (packagesError) {
      toast.show(packagesError, { type: "error" });
    }
  }, [propertyDetailsError, packagesError]);

  // TODO: Add Loader
  if (isPropertyDetailsLoading || isPackagesLoading)
    return <div>Loading...</div>;

  if (!propertyDetails) return <div>Property not found</div>;

  return (
    <>
      {selectedPackages && selectedPackages.length > 0 && (
        <div
          className={
            "fixed bottom-10 left-20 right-20 bg-blue-50 p-4 rounded-xl shadow-xl"
          }
        >
          <div className={"flex justify-between items-center"}>
            <h1 className={"text-xl font-medium"}>
              {selectedPackages.length} Packages Selected
            </h1>
            <button className={"btn-primary"}>Book Now</button>
          </div>
        </div>
      )}
      <Header />
      <div className={"p-6"}>
        <SearchHeader />
        <DetailsBanner propertyDetails={propertyDetails} />
        {packages && (
          <div className={"py-4"}>
            <h1 className={"mb-5 text-xl font-medium"}>Browse Packages</h1>
            <div
              className={"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"}
            >
              {packages.map((pkg) => (
                <PackageCard
                  packageData={pkg}
                  key={pkg._id}
                  onPackageChange={handlePackageChange}
                  selectedPackages={selectedPackages}
                />
              ))}
            </div>
          </div>
        )}
        {/*<div className={"py-4"}>*/}
        {/*    <h1 className={"mb-5 text-xl font-medium"}>Ratings & Reviews</h1>*/}
        {/*    <div className={"grid grid-cols-1 md:grid-cols-2 gap-4"}>*/}
        {/*        {sampleReviews.map((review) => (*/}
        {/*            <ReviewCard review={review}/>*/}
        {/*        ))}*/}
        {/*    </div>*/}
        {/*</div>*/}
      </div>
    </>
  );
};

export default DetailsPage;
