import React, { useState, useEffect } from 'react';
import { ShoppingCart, CreditCard } from 'lucide-react';
import type { IPackage } from '@/types/Package.ts';

interface SelectedPackagesSummaryProps {
  selectedPackages: IPackage[];
  onBookNow: () => void;
}

const SelectedPackagesSummary: React.FC<SelectedPackagesSummaryProps> = ({
  selectedPackages,
  onBookNow,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (selectedPackages.length > 0) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [selectedPackages.length]);

  const totalPrice = selectedPackages.reduce((total, pkg) => {
    const taxesAndFees = pkg.taxes.reduce((acc, tax) => acc + parseInt(tax.value), 0);
    return total + pkg.price + taxesAndFees;
  }, 0);

  const totalPackages = selectedPackages.length;

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 left-6 right-6 z-50 animate-slide-in-up">
      <div className="max-w-4xl mx-auto bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200/50">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-full">
              <ShoppingCart className="w-4 h-4 text-primary" />
            </div>
            <span className="text-gray-700 font-medium">
              {totalPackages} Package{totalPackages !== 1 ? 's' : ''} Selected
            </span>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-right">
              <span className="text-lg font-bold text-gray-900">${totalPrice}</span>
              <span className="text-sm text-gray-500 ml-1">total</span>
            </div>
            <button
              onClick={onBookNow}
              className="bg-gradient-to-r from-primary to-primary-dark text-white px-6 py-2 rounded-full font-semibold hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 focus:outline-none active:scale-[0.98] flex items-center gap-2"
            >
              <CreditCard className="w-4 h-4" />
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectedPackagesSummary;
