@import "tailwindcss";
@import "react-datepicker/dist/react-datepicker.css";

@theme{
    --color-primary: #2563EB;
    --color-primary-dark: #153885;
}

@layer base {
    body {
        @apply font-sans;
    }
}

@layer components {
    .btn-outline {
        @apply border border-primary text-primary px-4 py-2 rounded-md;
    }
    .btn-primary {
        @apply bg-gradient-to-r from-primary to-primary-dark text-white px-8 py-3 rounded-xl font-semibold  hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 focus:outline-none active:scale-[0.98];
    }
    .remove-icon{
        @apply [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-clear-button]:hidden appearance-none
    }
}

.react-datepicker-wrapper{
    width: 100%;
}
.react-datepicker {
    display: flex !important;

}
.react-datepicker-popper{
    z-index: 999 !important;
    background: white !important;
}

.react-datepicker__time-box {
    z-index: 999 !important;
    max-height: 230px;
    overflow-y: auto;
}