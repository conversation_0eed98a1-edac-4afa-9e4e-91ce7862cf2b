import { createBrowserRouter } from "react-router";
import Layout from "@/layout";
import Home from "@/pages/home";
import SearchPage from "@/pages/search";
import DetailsPage from "@/pages/details";
import CheckoutPage from "@/pages/checkout";

const routes = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "search",
        element: <SearchPage />,
      },
      {
        path: "property/:propertyId",
        element: <DetailsPage />,
      },
      {
        path: "checkout",
        element: <CheckoutPage />,
      },
    ],
  },
]);

export default routes;
