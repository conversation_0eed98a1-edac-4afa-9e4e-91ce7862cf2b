import React from 'react';
import { Plane } from 'lucide-react';

interface LoaderProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  overlay?: boolean;
}

const AirportLoader: React.FC<LoaderProps> = ({ 
  message = "Searching for the best deals...", 
  size = 'md',
  overlay = true 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  const containerClasses = overlay 
    ? 'fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClasses}>
      <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl p-8 max-w-sm mx-4 border border-white/20">
        {/* Airport Terminal Icon */}
        <div className="relative mb-6">
          {/* Control Tower */}
          <div className="flex justify-center mb-4">
            <div className="relative">
              {/* Tower Base */}
              <div className="w-3 h-16 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-sm mx-auto"></div>
              {/* Tower Top */}
              <div className="w-8 h-4 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-lg -mt-1 mx-auto relative">
                {/* Radar Dish */}
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <div className="w-6 h-1 bg-gray-600 rounded-full animate-spin origin-center"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Runway */}
          <div className="relative h-2 bg-gradient-to-r from-gray-300 via-gray-400 to-gray-300 rounded-full mb-4">
            {/* Runway Lights */}
            <div className="absolute top-0 left-0 w-full h-full flex justify-between items-center px-2">
              {[...Array(5)].map((_, i) => (
                <div 
                  key={i} 
                  className="w-1 h-1 bg-yellow-400 rounded-full animate-pulse"
                  style={{ animationDelay: `${i * 0.2}s` }}
                ></div>
              ))}
            </div>
          </div>

          {/* Flying Airplane */}
          <div className="relative h-12 overflow-hidden">
            <div className="absolute inset-0 flex items-center">
              <div className="animate-fly-across">
                <Plane 
                  className={`${sizeClasses[size]} text-primary transform rotate-45 drop-shadow-lg`}
                />
              </div>
            </div>
          </div>

          {/* Clouds */}
          <div className="absolute top-0 right-0 opacity-30">
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <div 
                  key={i}
                  className="w-3 h-2 bg-gray-300 rounded-full animate-float"
                  style={{ animationDelay: `${i * 0.5}s` }}
                ></div>
              ))}
            </div>
          </div>
        </div>

        {/* Loading Dots */}
        <div className="flex justify-center space-x-2 mb-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="w-3 h-3 bg-primary rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            ></div>
          ))}
        </div>

        {/* Loading Message */}
        <p className="text-center text-gray-700 font-medium text-sm">
          {message}
        </p>

        {/* Progress Bar */}
        <div className="mt-4 w-full bg-gray-200 rounded-full h-1 overflow-hidden">
          <div className="h-full bg-gradient-to-r from-primary to-primary-dark rounded-full animate-progress"></div>
        </div>
      </div>
    </div>
  );
};

export default AirportLoader;
