import React from "react";
import type { CheckoutCardProps } from "@/components/checkout-card/type.ts";
import { Bed, Users, Clock } from "lucide-react";

const CheckoutCard: React.FC<CheckoutCardProps> = ({
  imageUrl,
  hotelName,
  roomType,
  capacity,
  bedType,
  rate,
  taxesFees,
  checkIn,
  checkOut,
}) => {
  const total = rate * 2 + taxesFees;

  return (
    <div className="w-full bg-white rounded-lg shadow">
      <img
        className="w-full h-48 object-cover rounded-t-lg"
        src={imageUrl}
        alt="Room"
      />
      <div className="p-6">
        <h2 className="text-gray-700 text-lg font-semibold">{hotelName}</h2>
        <p className="text-gray-600 text-sm font-semibold">
          Room Type: {roomType}
        </p>

        <div className="flex items-center text-gray-600 text-sm mt-2 space-x-4">
          <div className="flex items-center space-x-1">
            <Users size={16} />
            <span>{capacity}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Bed size={16} />
            <span>{bedType}</span>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <span className="text-gray-700">${rate}/hr x 2</span>
          <span className="text-gray-500">(${taxesFees} taxes and fees)</span>
        </div>

        <p className="text-gray-900 font-bold mt-2">Total: ${total}/hr</p>

        <div className="flex items-center text-gray-600 text-sm mt-2 space-x-4">
          <div className="flex items-center space-x-1">
            <Clock size={16} />
            <span>Check-in: {checkIn}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock size={16} />
            <span>Check-out: {checkOut}</span>
          </div>
        </div>

        <button
          type="submit"
          className="w-full bg-blue-500 text-white py-2 mt-4 rounded hover:bg-blue-600"
        >
          Proceed To Pay
        </button>
      </div>
    </div>
  );
};

export default CheckoutCard;
