import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { toaster } from "./toaster";

export type ToastType = "info" | "success" | "error" | "warning";

export type Toast = {
  id: string;
  message: string;
  type?: ToastType;
  duration?: number;
};

export type ToastContextValue = {
  show: (
    _message: string,
    _options?: { type?: ToastType; duration?: number },
  ) => void;
  close: (_id: string) => void;
};

const ToastContext = createContext<ToastContextValue | undefined>(undefined);

export const useToast = () => {
  const ctx = useContext(ToastContext);
  if (!ctx) throw new Error("useToast must be used within ToastProvider");
  return ctx;
};

export const ToastProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  useEffect(() => {
    const unsub = toaster.subscribe((t) => {
      setToasts((prev) => [...prev, t]);
    });
    return () => {
      unsub();
    };
  }, []);

  const close = useCallback((id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  const show = useCallback(
    (message: string, options?: { type?: ToastType; duration?: number }) => {
      toaster.push(message, options);
    },
    [],
  );

  const value = useMemo(() => ({ show, close }), [show, close]);

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onClose={close} />
    </ToastContext.Provider>
  );
};

const typeStyles: Record<ToastType, React.CSSProperties> = {
  info: { backgroundColor: "#1e40af" },
  success: { backgroundColor: "#16a34a" },
  error: { backgroundColor: "#dc2626" },
  warning: { backgroundColor: "#d97706" },
};

const ToastContainer: React.FC<{
  toasts: Toast[];
  onClose: (_id: string) => void;
}> = ({ toasts, onClose }) => {
  return (
    <div
      aria-live="polite"
      aria-atomic="true"
      style={{
        position: "fixed",
        top: 16,
        right: 16,
        display: "flex",
        flexDirection: "column",
        gap: 8,
        zIndex: 9999,
        pointerEvents: "none",
      }}
    >
      {toasts.map((t) => (
        <ToastItem key={t.id} toast={t} onClose={() => onClose(t.id)} />
      ))}
    </div>
  );
};

const ToastItem: React.FC<{ toast: Toast; onClose: () => void }> = ({
  toast,
  onClose,
}) => {
  useEffect(() => {
    if (toast.duration && toast.duration > 0) {
      const id = setTimeout(onClose, toast.duration);
      return () => clearTimeout(id);
    }
  }, [toast.duration, onClose]);

  return (
    <div
      role="status"
      style={{
        pointerEvents: "auto",
        color: "white",
        minWidth: 280,
        maxWidth: 420,
        boxShadow:
          "0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1)",
        borderRadius: 8,
        padding: "10px 12px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 12,
        ...typeStyles[toast.type || "info"],
      }}
    >
      <span style={{ lineHeight: 1.3 }}>{toast.message}</span>
      <button
        onClick={onClose}
        aria-label="Close"
        style={{
          background: "transparent",
          border: "none",
          color: "white",
          cursor: "pointer",
          fontSize: 18,
          lineHeight: 1,
          padding: 4,
        }}
      >
        ×
      </button>
    </div>
  );
};
