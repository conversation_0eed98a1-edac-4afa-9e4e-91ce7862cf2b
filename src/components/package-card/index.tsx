import {
  Info,
  Luc<PERSON><PERSON>edDouble,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LucideSquareDot,
  Minus,
  Plus,
} from "lucide-react";
import React from "react";
import type { PackageCardProps } from "@/components/package-card/type.ts";

const PackageCard: React.FC<PackageCardProps> = ({
  packageData,
  onPackageChange,
  selectedPackages,
}) => {
  const unitType = packageData.unitTypeId;

  const taxesAndFees = packageData.taxes.reduce(
    (acc, tax) => acc + parseInt(tax.value),
    0,
  );
  const totalPrice = packageData.price + taxesAndFees;

  return (
    <div className="h-[650px] bg-white rounded-xl shadow-md">
      <img
        className="w-full h-60 object-cover rounded-t-xl"
        src={unitType.attachments[0]}
        alt={unitType.name}
      />
      <div className="p-6">
        <div className="flex flex-col gap-2 justify-between">
          <h2 className="text-xl font-bold">{packageData.name}</h2>
          <div className="flex items-center space-x-2 text-gray-500 text-sm">
            <LucideSquareDot className="w-5 h-5" />
            <span>{unitType.area}</span>
            <LucideBedDouble className="w-5 h-5" />
            <span>
              {unitType.bedType} (Sleeps {packageData.noOfAdults} Adults)
            </span>
          </div>
        </div>
        <div className="mt-4">
          <h3 className="text-lg font-semibold">Included:</h3>
          <ul className="mt-2 space-y-2 text-gray-500 text-sm">
            {packageData.amenities &&
              packageData.amenities.slice(0, 3).map((amenity, index) => (
                <li key={index} className="flex items-center space-x-2">
                  <LucideCheck className="w-5 h-5 text-green-500" />
                  <span>{amenity.name}</span>
                </li>
              ))}
          </ul>
          <a className="text-gray-500 hover:text-primary text-sm mt-2 inline-block">
            View more
          </a>
        </div>
        <div className="mt-4">
          <div className="flex items-center gap-4">
            <p className="text-lg font-medium">${packageData.price}</p>
            <p className="text-gray-500 text-sm flex items-center gap-1">
              <Info className="w-4 h-4" />{" "}
              <span>(${taxesAndFees} taxes and fees)</span>
            </p>
          </div>
          <p className="text-xl font-bold">Total: ${totalPrice}/hr</p>
        </div>
        <div className="mt-4 w-full">
          {selectedPackages?.some((pkg) => pkg._id === packageData._id) ? (
            <div className="flex items-center justify-between  py-2 px-4 rounded-lg border border-primary">
              <button
                onClick={() => onPackageChange(packageData, "remove")}
                className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Remove package"
              >
                <Minus size={20} />
              </button>
              <span className="text-lg font-semibold">
                {
                  selectedPackages?.filter((pkg) => pkg._id === packageData._id)
                    .length
                }
              </span>
              <button
                onClick={() => onPackageChange(packageData, "add")}
                className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Add another package"
              >
                <Plus size={20} />
              </button>
            </div>
          ) : (
            <button
              onClick={() => onPackageChange(packageData, "add")}
              className="w-full bg-primary text-white py-2 rounded-lg hover:bg-white hover:text-primary hover:border hover:border-primary transition-colors duration-400 ease-in-out"
            >
              Select Package
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PackageCard;
