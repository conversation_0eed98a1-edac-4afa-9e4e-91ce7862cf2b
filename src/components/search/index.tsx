import { useEffect, useMemo, useState } from "react";
import { CalendarIcon, MapPin } from "lucide-react";
import Select from "react-select";
import DatePicker from "react-datepicker";
import { selectStyles } from "@/components/search/style.ts";
import AirportLoader from "@/components/loader";
import useAxios from "@/hooks/useAxios.tsx";
import type { ILocation } from "@/types";
import { useNavigate } from "react-router";
import { useToast } from "@/components/toast/ToasterProvider.tsx";
import { getFuture15MinuteInterval, getIntervalTime, utcInIso } from "@/utils";

interface SelectOption {
  label: string;
  value: string;
}

const Search = () => {
  const toast = useToast();
  const {
    data: locations,
    loading: isLocationLoading,
    request: getLocations,
    error: locationError,
  } = useAxios<ILocation[]>();
  const [location, setLocation] = useState<SelectOption | null>(null);
  const [checkIn, setCheckIn] = useState<Date | null>(
    getFuture15MinuteInterval(new Date()),
  );
  const [checkOut, setCheckOut] = useState<Date | null>(
    getFuture15MinuteInterval(getIntervalTime(new Date())),
  );

  const navigate = useNavigate();

  useEffect(() => {
    const getLocationsData = async () => {
      await getLocations({
        url: "/locations",
        method: "GET",
      });
    };
    getLocationsData();
  }, []);

  useEffect(() => {
    if (locationError) {
      toast.show(locationError, { type: "error" });
    }
  }, [locationError]);

  const handleSearch = () => {
    if (!location) {
      toast.show("Please select a location", { type: "error" });
      return;
    }
    if (!checkIn) {
      toast.show("Please select a check-in date", { type: "error" });
      return;
    }
    if (!checkOut) {
      toast.show("Please select a check-out date", { type: "error" });
      return;
    }
    const queryParams = new URLSearchParams();
    queryParams.set("location", location.value);
    queryParams.set("checkIn", utcInIso(checkIn));
    queryParams.set("checkOut", utcInIso(checkOut));
    navigate({
      pathname: "/search",
      search: queryParams.toString(),
    });
  };

  const locationOptions: SelectOption[] = useMemo(() => {
    return (
      locations?.map((loc) => ({
        label: `${loc.city}, ${loc.country}`,
        value: loc._id,
      })) || []
    );
  }, [locations]);

  useEffect(() => {
    if (locationOptions && locationOptions.length > 0) {
      setLocation(locationOptions[0]);
    }
  }, [locationOptions]);

  if (isLocationLoading)
    return (
      <AirportLoader
        message="Loading available destinations..."
        size="sm"
        overlay={false}
      />
    );

  return (
    <div className="flex items-center justify-center p-4">
      <div className="max-w-6xl w-full">
        <div className="relative backdrop-blur-xs border border-white rounded-2xl px-6 py-8">
          <h1 className="capitalize text-white font-medium text-sm mb-2">
            Tell us how much time you have
          </h1>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border border-white/30 rounded-xl">
            {/* Location Dropdown */}
            <div className="w-full flex items-center gap-0.5 px-2 relative z-10 backdrop-blur-sm">
              <MapPin className="w-5 h-5 text-white" />
              <Select
                options={locationOptions}
                value={location}
                className="w-full text-gray-500"
                onChange={(selectedOption) => setLocation(selectedOption)}
                styles={selectStyles}
                placeholder="Select a location"
              />
            </div>

            {/* Check-in Date and Time */}
            <div className="relative border-l border-white/20">
              <div className="w-full px-4 py-4 text-white">
                <div className="w-full flex items-center gap-2 relative z-50">
                  <CalendarIcon className="w-5 h-5 text-white" />
                  <DatePicker
                    selected={checkIn}
                    onChange={(date) => {
                      if (!date) return;
                      const newDate = getFuture15MinuteInterval(date);
                      setCheckIn(date);
                      setCheckOut(getIntervalTime(newDate));
                    }}
                    minDate={new Date()}
                    showTimeSelect
                    timeIntervals={15}
                    timeFormat="HH:mm"
                    dateFormat="dd-MM-yyyy HH:mm"
                    className="w-full text-md bg-transparent text-white !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                    placeholderText="Select check-in date and time"
                  />
                </div>
              </div>
            </div>

            {/* Check-out Date and Time */}
            <div className="relative border-l border-white/20">
              <div className="w-full px-4 py-4 text-white">
                <div className="w-full flex items-center gap-2 relative">
                  <CalendarIcon className="w-5 h-5 text-white" />
                  <DatePicker
                    selected={checkOut}
                    onChange={(date) => setCheckOut(date)}
                    minDate={checkIn || new Date()}
                    showTimeSelect
                    timeIntervals={15}
                    timeFormat="HH:mm"
                    dateFormat="dd-MM-yyyy HH:mm"
                    className="w-full text-md bg-transparent text-white !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                    popperClassName="z-50"
                    placeholderText="Select check-out date and time"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute left-0 -bottom-10 right-0 text-center">
            <button
              className="btn-primary text-white font-medium px-12 py-4 rounded-xl text-lg transition-all duration-200 hover:scale-105 shadow-xl"
              onClick={handleSearch}
            >
              Explore Services
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
