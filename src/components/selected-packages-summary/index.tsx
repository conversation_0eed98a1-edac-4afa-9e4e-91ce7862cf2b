import React, { useState, useEffect } from 'react';
import { ShoppingCart, X, Package, CreditCard, Users, Clock } from 'lucide-react';
import type { IPackage } from '@/types/Package.ts';

interface SelectedPackagesSummaryProps {
  selectedPackages: IPackage[];
  onRemovePackage: (pkg: IPackage) => void;
  onBookNow: () => void;
}

const SelectedPackagesSummary: React.FC<SelectedPackagesSummaryProps> = ({
  selectedPackages,
  onRemovePackage,
  onBookNow,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [animatingItems, setAnimatingItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (selectedPackages.length > 0) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [selectedPackages.length]);

  const handleRemovePackage = (pkg: IPackage) => {
    setAnimatingItems(prev => new Set(prev).add(pkg._id));
    setTimeout(() => {
      onRemovePackage(pkg);
      setAnimatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(pkg._id);
        return newSet;
      });
    }, 300);
  };

  // Group packages by type and count
  const packageGroups = selectedPackages.reduce((acc, pkg) => {
    const existing = acc.find(group => group.package._id === pkg._id);
    if (existing) {
      existing.count++;
    } else {
      acc.push({ package: pkg, count: 1 });
    }
    return acc;
  }, [] as { package: IPackage; count: number }[]);

  const totalPrice = selectedPackages.reduce((total, pkg) => {
    const taxesAndFees = pkg.taxes.reduce((acc, tax) => acc + parseInt(tax.value), 0);
    return total + pkg.price + taxesAndFees;
  }, 0);

  const totalPackages = selectedPackages.length;

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 left-6 right-6 z-50 animate-slide-in-up">
      <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-primary-dark text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <ShoppingCart className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Selected Packages</h3>
                <p className="text-white/80 text-sm">
                  {totalPackages} package{totalPackages !== 1 ? 's' : ''} selected
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-white/80 text-sm">Total</p>
              <p className="text-2xl font-bold">${totalPrice}</p>
            </div>
          </div>
        </div>

        {/* Package List */}
        <div className="p-4 max-h-60 overflow-y-auto">
          <div className="space-y-3">
            {packageGroups.map(({ package: pkg, count }) => {
              const taxesAndFees = pkg.taxes.reduce((acc, tax) => acc + parseInt(tax.value), 0);
              const itemTotal = (pkg.price + taxesAndFees) * count;
              const isAnimating = animatingItems.has(pkg._id);

              return (
                <div
                  key={pkg._id}
                  className={`flex items-center gap-4 p-3 bg-gray-50 rounded-xl transition-all duration-300 ${
                    isAnimating ? 'animate-slide-out-down opacity-50' : 'animate-slide-in-up'
                  }`}
                >
                  {/* Package Image */}
                  <div className="flex-shrink-0">
                    <img
                      src={pkg.unitTypeId.attachments[0]}
                      alt={pkg.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                  </div>

                  {/* Package Details */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 truncate">{pkg.name}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{pkg.noOfAdults} Adults</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Package className="w-4 h-4" />
                        <span>{pkg.unitTypeId.bedType}</span>
                      </div>
                    </div>
                  </div>

                  {/* Quantity and Price */}
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                        {count}
                      </div>
                      <span className="text-xs text-gray-500 mt-1">Qty</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">${itemTotal}</p>
                      <p className="text-xs text-gray-500">${pkg.price + taxesAndFees} each</p>
                    </div>
                    <button
                      onClick={() => handleRemovePackage(pkg)}
                      disabled={isAnimating}
                      className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50"
                      aria-label="Remove package"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>Hourly rates apply</span>
              </div>
              <div className="flex items-center gap-1">
                <Package className="w-4 h-4" />
                <span>Taxes included</span>
              </div>
            </div>
            <button
              onClick={onBookNow}
              className="bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 focus:outline-none active:scale-[0.98] flex items-center gap-2"
            >
              <CreditCard className="w-5 h-5" />
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectedPackagesSummary;
