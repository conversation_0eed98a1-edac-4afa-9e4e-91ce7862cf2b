import React, { useState, useEffect } from 'react';
import { ShoppingCart, CreditCard } from 'lucide-react';
import type { IPackage } from '@/types/Package.ts';

interface SelectedPackagesSummaryProps {
  selectedPackages: IPackage[];
  onBookNow: () => void;
}

const SelectedPackagesSummary: React.FC<SelectedPackagesSummaryProps> = ({
  selectedPackages,
  onBookNow,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (selectedPackages.length > 0) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [selectedPackages.length]);

  const totalPrice = selectedPackages.reduce((total, pkg) => {
    const taxesAndFees = pkg.taxes.reduce((acc, tax) => acc + parseInt(tax.value), 0);
    return total + pkg.price + taxesAndFees;
  }, 0);

  const totalPackages = selectedPackages.length;

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 left-6 right-6 z-50 animate-slide-in-up">
      <div className="max-w-2xl mx-auto bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
        <div className="bg-gradient-to-r from-primary to-primary-dark text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <ShoppingCart className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-xl font-bold">
                  {totalPackages} Package{totalPackages !== 1 ? 's' : ''} Selected
                </h3>
                <p className="text-white/80 text-sm">Ready to book your stay</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-white/80 text-sm">Total Amount</p>
              <p className="text-3xl font-bold">${totalPrice}</p>
              <p className="text-white/60 text-xs">taxes included</p>
            </div>
          </div>
          <div className="mt-6 flex justify-center">
            <button
              onClick={onBookNow}
              className="bg-white text-primary px-8 py-3 rounded-xl font-bold hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 focus:outline-none active:scale-[0.98] flex items-center gap-3 text-lg"
            >
              <CreditCard className="w-6 h-6" />
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectedPackagesSummary;
