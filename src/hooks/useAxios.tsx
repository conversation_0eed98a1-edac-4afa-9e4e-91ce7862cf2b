import { useState, useCallback } from "react";
import type { AxiosRequestConfig, AxiosResponse } from "axios";
import axiosInstance from "@/config/base";

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

/**
 * Custom hook for making API requests using Axios.
 * @param T - The type of the data expected in the response.
 * @template T - The type of the data expected in the response.
 * @returns Object containing data, loading state, error, request function, and reset function.
 */

function useAxios<T = unknown>() {
  const getMessageFromError = (err: unknown): string => {
    if (typeof err === "string") return err;
    if (typeof err === "object" && err !== null) {
      const maybe = err as { message?: string; response?: { data?: unknown } };
      if (maybe.response && typeof maybe.response === "object") {
        const data = maybe.response.data as Record<string, unknown> | undefined;
        const msg =
          (data?.message as string | undefined) ??
          (data?.error as string | undefined);
        if (msg) return msg;
      }
      if (maybe.message) return maybe.message;
    }
    return "An error occurred";
  };
  const [response, setResponse] = useState<ApiResponse<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const request = useCallback(
    async (config: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
      setResponse((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const result = await axiosInstance(config);
        setResponse({ data: result.data.data, loading: false, error: null });
        return result;
      } catch (err: unknown) {
        const message = getMessageFromError(err);
        setResponse({ data: null, loading: false, error: message });
        throw err;
      }
    },
    [],
  );

  // Reset state function
  const reset = useCallback(() => {
    setResponse({ data: null, loading: false, error: null });
  }, []);

  return { ...response, request, reset };
}

export default useAxios;
